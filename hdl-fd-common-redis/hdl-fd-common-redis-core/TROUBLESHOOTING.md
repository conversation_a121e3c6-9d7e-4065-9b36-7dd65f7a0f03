# HDL Redis Core 故障排除指南

## 配置属性注入失败问题

### 问题现象
- `RedisConfigProperties` 注入后所有属性都是 `null`
- 日志显示 "Redis配置为空"
- Redis相关Bean无法正常创建

### 排查步骤

#### 1. 检查配置文件格式

确保你的 `application.yml` 或 `application.properties` 格式正确：

**正确的YAML格式：**
```yaml
hdl:
  redis:
    single:
      url: localhost:6379
      database: 0
      password: your_password
```

**常见错误：**
```yaml
# ❌ 错误：缩进不正确
hdl:
redis:
  single:
    url: localhost:6379

# ❌ 错误：使用了tab而不是空格
hdl:
	redis:
		single:
			url: localhost:6379

# ❌ 错误：配置前缀不匹配
spring:
  redis:  # 应该是 hdl.redis
    single:
      url: localhost:6379
```

#### 2. 检查配置文件位置

确保配置文件在正确的位置：
- `src/main/resources/application.yml`
- `src/main/resources/application.properties`
- 或者通过 `--spring.config.location` 指定的位置

#### 3. 检查Profile配置

如果使用了Profile，确保配置在正确的Profile下：

```yaml
# 默认配置
hdl:
  redis:
    single:
      url: localhost:6379

---
# 开发环境配置
spring:
  profiles: dev
hdl:
  redis:
    single:
      url: dev-redis:6379

---
# 生产环境配置
spring:
  profiles: prod
hdl:
  redis:
    single:
      url: prod-redis:6379
```

启动时指定Profile：
```bash
java -jar app.jar --spring.profiles.active=dev
```

#### 4. 检查依赖和自动配置

确保以下依赖已正确添加：

```xml
<dependency>
    <groupId>com.haidilao.fd</groupId>
    <artifactId>hdl-fd-common-redis-core</artifactId>
    <version>${revision}</version>
</dependency>

<!-- Spring Boot Configuration Processor -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-configuration-processor</artifactId>
    <optional>true</optional>
</dependency>
```

#### 5. 启用调试日志

在 `application.yml` 中添加：

```yaml
logging:
  level:
    com.haidilao.fd.redis.core: DEBUG
    org.springframework.boot.context.properties: DEBUG
    org.springframework.context.annotation: DEBUG
```

#### 6. 使用配置诊断工具

启动应用后，查看控制台输出的配置诊断信息：

```
=== HDL Redis 配置诊断 ===
检查配置前缀: hdl.redis
  找到配置: hdl.redis.single.url = localhost:6379
  找到配置: hdl.redis.single.database = 0
检查具体配置项:
  单Redis配置: hdl.redis.single.url = localhost:6379
  单Redis数据库: hdl.redis.single.database = 0
当前激活的Profile: dev
=== 配置诊断完成 ===
```

### 常见问题和解决方案

#### 问题1：配置文件编码问题

**现象：** 配置文件包含中文注释，导致解析失败

**解决：** 确保配置文件使用UTF-8编码

#### 问题2：配置属性名称错误

**现象：** 配置了但是读取不到

**检查：** 确保属性名称与 `RedisConfigProperties` 中的字段名完全一致

```java
// RedisConfigProperties.java
private Map<String, RedisEnvironmentConfig> datasources;  // 对应 hdl.redis.datasources
private RedisEnvironmentConfig single;                    // 对应 hdl.redis.single
private String defaultDatasource;                         // 对应 hdl.redis.defaultDatasource
```

#### 问题3：Spring Boot版本兼容性

**现象：** `@ConfigurationProperties` 不生效

**解决：** 确保Spring Boot版本兼容，建议使用2.3+版本

#### 问题4：自动配置被排除

**现象：** 配置类没有被加载

**检查：** 确保没有在 `@SpringBootApplication` 中排除相关配置：

```java
// ❌ 错误：排除了配置处理器
@SpringBootApplication(exclude = {ConfigurationPropertiesAutoConfiguration.class})

// ✅ 正确：不排除配置处理器
@SpringBootApplication
```

#### 问题5：配置文件优先级问题

**现象：** 配置被其他配置文件覆盖

**解决：** 检查配置文件加载顺序，Spring Boot配置文件优先级：
1. 命令行参数
2. `application-{profile}.properties/yml`
3. `application.properties/yml`
4. jar包内的配置文件

### 调试技巧

#### 1. 添加调试代码

在配置类中添加调试信息：

```java
@Bean
public RedisDataSourceProcessor redisDataSourceProcessor(RedisConfigProperties redisProperties) {
    log.info("Redis配置详情:");
    log.info("  datasources: {}", redisProperties.getDatasources());
    log.info("  single: {}", redisProperties.getSingle());
    log.info("  defaultDatasource: {}", redisProperties.getDefaultDatasource());
    
    return new RedisDataSourceProcessor(redisProperties);
}
```

#### 2. 使用Actuator端点

添加依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

访问配置端点：
```
GET /actuator/configprops
```

查找 `hdl.redis` 相关配置。

#### 3. 环境变量检查

如果使用环境变量，确保格式正确：

```bash
# 正确格式
export HDL_REDIS_SINGLE_URL=localhost:6379
export HDL_REDIS_SINGLE_DATABASE=0

# 或者在application.yml中引用
hdl:
  redis:
    single:
      url: ${HDL_REDIS_SINGLE_URL:localhost:6379}
      database: ${HDL_REDIS_SINGLE_DATABASE:0}
```

### 完整的测试配置示例

创建一个最小化的测试配置：

**application-test.yml:**
```yaml
hdl:
  redis:
    single:
      url: localhost:6379
      database: 0
      mode: standalone
      maxActive: 8
      maxIdle: 8
      minIdle: 0
      maxWait: -1

logging:
  level:
    com.haidilao.fd.redis.core: DEBUG
```

**启动参数：**
```bash
java -jar app.jar --spring.profiles.active=test
```

如果这个最小配置都不工作，那么问题可能在于：
1. 依赖版本冲突
2. 类路径问题
3. Spring Boot自动配置被意外禁用

### 联系支持

如果以上步骤都无法解决问题，请提供以下信息：
1. 完整的配置文件内容
2. 启动日志（包括调试信息）
3. 依赖版本信息（`mvn dependency:tree`）
4. Spring Boot版本
5. JDK版本
