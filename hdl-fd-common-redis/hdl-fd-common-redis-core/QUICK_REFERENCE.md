# HDL Redis Core 快速参考

## 快速配置

### 单Redis
```yaml
hdl:
  redis:
    single:
      url: localhost:6379
      database: 0
      password: your_password
```

### 多Redis
```yaml
hdl:
  redis:
    defaultDatasource: cache
    datasources:
      cache:
        url: localhost:6379
        database: 0
      session:
        url: localhost:6380
        database: 1
```

## 常用API速查

### 基础操作
```java
// 设置/获取
redisUtil.set("key", value, 3600);
User user = redisUtil.get("key", User.class);

// 删除/检查存在
redisUtil.delete("key");
Boolean exists = redisUtil.hasKey("key");

// 过期时间
redisUtil.expire("key", 3600);
Long ttl = redisUtil.getExpire("key");
```

### String操作
```java
// 数值操作
Long newValue = redisUtil.incr("counter");
Long result = redisUtil.incrBy("counter", 5);

// 批量操作
redisUtil.multiSet(Map.of("k1", "v1", "k2", "v2"));
List<String> values = redisUtil.multiGet(Arrays.asList("k1", "k2"), String.class);
```

### Hash操作
```java
// 单个字段
redisUtil.hPut("user:1", "name", "张三");
String name = redisUtil.hGet("user:1", "name", String.class);

// 批量操作
redisUtil.hPutAll("user:1", userMap);
Map<Object, Object> all = redisUtil.hGetAll("user:1");

// 删除字段
redisUtil.hDelete("user:1", "field1", "field2");
```

### List操作
```java
// 添加元素
redisUtil.lLeftPush("queue", "item");      // 头部添加
redisUtil.lRightPush("queue", "item");     // 尾部添加

// 弹出元素
String item = redisUtil.lLeftPop("queue", String.class);   // 头部弹出
String item = redisUtil.lRightPop("queue", String.class);  // 尾部弹出

// 获取范围
List<String> items = redisUtil.lRange("queue", 0, -1, String.class);
```

### Set操作
```java
// 添加/删除
redisUtil.sAdd("tags", "java", "spring");
redisUtil.sRemove("tags", "java");

// 获取所有/检查存在
Set<String> all = redisUtil.setMembers("tags", String.class);
Boolean exists = redisUtil.sIsMember("tags", "java");

// 集合运算
Set<String> intersection = redisUtil.sIntersect("set1", "set2", String.class);
```

### Sorted Set操作
```java
// 添加元素
redisUtil.zAdd("leaderboard", "player1", 100.0);

// 获取排名/分数
Long rank = redisUtil.zRank("leaderboard", "player1");
Double score = redisUtil.zScore("leaderboard", "player1");

// 范围查询
Set<String> top10 = redisUtil.zReverseRange("leaderboard", 0, 9, String.class);
```

## 多Redis使用

### 注入方式
```java
@Service
public class UserService {
    
    @Autowired
    @Qualifier("cacheRedisUtil")
    private RedisUtil cacheRedisUtil;
    
    @Autowired
    @Qualifier("sessionRedisUtil")
    private RedisUtil sessionRedisUtil;
}
```

### Bean命名规则
- 单Redis: `redisUtil`, `redisTemplate`, `stringRedisTemplate`
- 多Redis: `{name}RedisUtil`, `{name}RedisTemplate`, `{name}StringRedisTemplate`

## 部署模式配置

### Standalone
```yaml
hdl:
  redis:
    single:
      url: localhost:6379
      mode: standalone
```

### Cluster
```yaml
hdl:
  redis:
    single:
      url: node1:6379;node2:6379;node3:6379
      mode: cluster
```

### Sentinel
```yaml
hdl:
  redis:
    single:
      url: sentinel1:26379;sentinel2:26379
      mode: sentinel
      master: mymaster
```

## 连接池配置
```yaml
hdl:
  redis:
    single:
      maxActive: 20    # 最大连接数
      maxIdle: 10      # 最大空闲连接
      minIdle: 2       # 最小空闲连接
      maxWait: 5000    # 最大等待时间(ms)
```

## 常见错误

### 1. Bean注入失败
```java
// ❌ 错误：多Redis时未指定Qualifier
@Autowired
private RedisUtil redisUtil;

// ✅ 正确：指定具体的Bean
@Autowired
@Qualifier("cacheRedisUtil")
private RedisUtil redisUtil;
```

### 2. 类型转换错误
```java
// ❌ 错误：手动类型转换
Object obj = redisUtil.get("key");
User user = (User) obj;

// ✅ 正确：使用泛型方法
User user = redisUtil.get("key", User.class);
```

### 3. 忘记设置过期时间
```java
// ❌ 错误：可能导致内存泄漏
redisUtil.set("temp_data", data);

// ✅ 正确：设置合理的过期时间
redisUtil.set("temp_data", data, 3600);
```

## 性能优化提示

1. **批量操作**: 使用`multiSet`、`multiGet`等批量方法
2. **合理过期**: 为临时数据设置过期时间
3. **连接池**: 根据并发量调整连接池参数
4. **数据类型**: 根据场景选择合适的Redis数据类型
5. **Key设计**: 使用有意义且规范的Key命名

## 调试技巧

### 启用调试日志
```yaml
logging:
  level:
    com.haidilao.fd.redis.core: DEBUG
```

### 健康检查
```java
try {
    redisUtil.set("health", "ok", 10);
    String result = redisUtil.get("health", String.class);
    System.out.println("Redis健康状态: " + ("ok".equals(result) ? "正常" : "异常"));
} catch (Exception e) {
    System.out.println("Redis连接失败: " + e.getMessage());
}
```
