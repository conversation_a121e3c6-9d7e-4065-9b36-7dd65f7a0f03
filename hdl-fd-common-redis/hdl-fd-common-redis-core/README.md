# HDL Redis Core 使用文档

## 概述

`hdl-fd-common-redis-core` 是海底捞Redis工具模块的核心组件，提供了对Spring Data Redis的封装和增强，支持单Redis和多Redis数据源配置，并提供了丰富的Redis操作工具类。

## 主要特性

- ✅ **多Redis数据源支持**：支持在同一个应用中配置多个Redis数据源
- ✅ **自动配置**：完全替代Spring Boot默认Redis配置，提供更灵活的配置选项
- ✅ **连接池支持**：基于Lettuce连接池，支持连接池参数配置
- ✅ **多种部署模式**：支持Standalone、Cluster、Sentinel三种Redis部署模式
- ✅ **类型安全**：RedisUtil提供泛型支持，避免类型转换错误
- ✅ **丰富的操作API**：封装了Redis所有数据类型的操作方法

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.haidilao.fd</groupId>
    <artifactId>hdl-fd-common-redis-core</artifactId>
    <version>${revision}</version>
</dependency>
```

### 2. 配置Redis

#### 单Redis配置

```yaml
hdl:
  redis:
    single:
      url: localhost:6379
      database: 0
      password: your_password  # 可选
      mode: standalone        # 默认值
      maxActive: 8           # 连接池最大连接数
      maxIdle: 8             # 连接池最大空闲连接数
      minIdle: 0             # 连接池最小空闲连接数
      maxWait: -1            # 连接池最大等待时间(毫秒)
```

#### 多Redis配置

```yaml
hdl:
  redis:
    defaultDatasource: cache  # 指定默认数据源
    datasources:
      cache:
        url: localhost:6379
        database: 0
        mode: standalone
        maxActive: 8
        maxIdle: 8
        minIdle: 0
        maxWait: -1
      session:
        url: localhost:6380
        database: 1
        mode: standalone
        maxActive: 8
        maxIdle: 8
        minIdle: 0
        maxWait: -1
```

#### 集群模式配置

```yaml
hdl:
  redis:
    single:
      url: node1:6379;node2:6379;node3:6379  # 多个节点用分号分隔
      mode: cluster
      password: your_password
      maxActive: 8
      maxIdle: 8
      minIdle: 0
      maxWait: -1
```

#### 哨兵模式配置

```yaml
hdl:
  redis:
    single:
      url: sentinel1:26379;sentinel2:26379;sentinel3:26379  # 哨兵节点
      mode: sentinel
      master: mymaster  # 主节点名称
      database: 0
      password: your_password
      maxActive: 8
      maxIdle: 8
      minIdle: 0
      maxWait: -1
```

### 3. 使用RedisUtil

#### 单Redis使用

```java
@Service
public class UserService {
    
    @Autowired
    private RedisUtil redisUtil;
    
    public void saveUser(User user) {
        // 保存对象，自动序列化
        redisUtil.set("user:" + user.getId(), user, 3600); // 1小时过期
    }
    
    public User getUser(Long userId) {
        // 获取对象，指定类型
        return redisUtil.get("user:" + userId, User.class);
    }
}
```

#### 多Redis使用

```java
@Service
public class UserService {
    
    @Autowired
    @Qualifier("cacheRedisUtil")
    private RedisUtil cacheRedisUtil;
    
    @Autowired
    @Qualifier("sessionRedisUtil")
    private RedisUtil sessionRedisUtil;
    
    public void saveUserCache(User user) {
        cacheRedisUtil.set("user:" + user.getId(), user, 3600);
    }
    
    public void saveUserSession(String sessionId, User user) {
        sessionRedisUtil.set("session:" + sessionId, user, 1800);
    }
}
```

## RedisUtil API 详解

### 基础Key操作

```java
// 删除key
redisUtil.delete("key");
redisUtil.delete(Arrays.asList("key1", "key2", "key3"));

// 检查key是否存在
Boolean exists = redisUtil.hasKey("key");

// 设置过期时间
redisUtil.expire("key", 3600); // 秒
redisUtil.expire("key", 1, TimeUnit.HOURS); // 指定时间单位

// 获取剩余过期时间
Long ttl = redisUtil.getExpire("key");

// 移除过期时间
redisUtil.persist("key");
```

### String操作

```java
// 基础设置和获取
redisUtil.set("key", "value");
redisUtil.set("key", "value", 3600); // 带过期时间
String value = redisUtil.get("key", String.class);

// 对象操作（推荐使用泛型方法）
User user = new User("张三", 25);
redisUtil.set("user:1", user);
User savedUser = redisUtil.get("user:1", User.class);

// 批量操作
Map<String, Object> map = new HashMap<>();
map.put("key1", "value1");
map.put("key2", "value2");
redisUtil.multiSet(map);

List<String> keys = Arrays.asList("key1", "key2");
List<String> values = redisUtil.multiGet(keys, String.class);

// 原子操作
Long newValue = redisUtil.incr("counter"); // 自增1
Long result = redisUtil.incrBy("counter", 5); // 增加5
```

### Hash操作

```java
// 基础Hash操作
redisUtil.hPut("user:1", "name", "张三");
redisUtil.hPut("user:1", "age", 25);
String name = redisUtil.hGet("user:1", "name", String.class);

// 批量设置
Map<String, Object> userInfo = new HashMap<>();
userInfo.put("name", "李四");
userInfo.put("age", 30);
userInfo.put("email", "<EMAIL>");
redisUtil.hPutAll("user:2", userInfo);

// 获取所有字段
Map<Object, Object> allFields = redisUtil.hGetAll("user:1");

// 获取所有字段名
Set<Object> fields = redisUtil.hKeys("user:1");

// 获取所有值
List<Object> values = redisUtil.hValues("user:1");

// 删除字段
redisUtil.hDelete("user:1", "email");

// 数值操作
redisUtil.hIncrBy("user:1", "loginCount", 1);
```

### List操作

```java
// 左侧插入（头部）
redisUtil.lLeftPush("queue", "item1");
redisUtil.lLeftPushAll("queue", "item2", "item3", "item4");

// 右侧插入（尾部）
redisUtil.lRightPush("queue", "item5");
redisUtil.lRightPushAll("queue", Arrays.asList("item6", "item7"));

// 弹出元素
String leftItem = redisUtil.lLeftPop("queue", String.class);
String rightItem = redisUtil.lRightPop("queue", String.class);

// 获取范围内元素
List<String> items = redisUtil.lRange("queue", 0, -1, String.class); // 获取所有
List<String> firstThree = redisUtil.lRange("queue", 0, 2, String.class); // 前3个

// 获取指定位置元素
String item = redisUtil.lIndex("queue", 0, String.class);

// 设置指定位置元素
redisUtil.lSet("queue", 0, "newItem");

// 获取列表长度
Long length = redisUtil.lLen("queue");

// 删除元素
redisUtil.lRemove("queue", 1, "item1"); // 删除1个"item1"
```

### Set操作

```java
// 添加元素
redisUtil.sAdd("tags", "java", "spring", "redis");

// 获取所有元素
Set<String> allTags = redisUtil.setMembers("tags", String.class);

// 检查元素是否存在
Boolean isMember = redisUtil.sIsMember("tags", "java");

// 随机获取元素
String randomTag = redisUtil.sRandomMember("tags", String.class);
Set<String> randomTags = redisUtil.sRandomMembers("tags", 2, String.class);

// 弹出元素
String poppedTag = redisUtil.sPop("tags", String.class);

// 移除元素
redisUtil.sRemove("tags", "java", "spring");

// 集合运算
Set<String> intersection = redisUtil.sIntersect("tags1", "tags2", String.class);
Set<String> union = redisUtil.sUnion("tags1", "tags2", String.class);
Set<String> difference = redisUtil.sDifference("tags1", "tags2", String.class);
```

### Sorted Set操作

```java
// 添加元素
redisUtil.zAdd("leaderboard", "player1", 100.0);
redisUtil.zAdd("leaderboard", "player2", 200.0);
redisUtil.zAdd("leaderboard", "player3", 150.0);

// 获取排名（从小到大，0开始）
Long rank = redisUtil.zRank("leaderboard", "player2");

// 获取倒序排名（从大到小，0开始）
Long reverseRank = redisUtil.zReverseRank("leaderboard", "player2");

// 获取分数
Double score = redisUtil.zScore("leaderboard", "player2");

// 增加分数
Double newScore = redisUtil.zIncrementScore("leaderboard", "player1", 50.0);

// 获取范围内元素（按排名）
Set<String> topPlayers = redisUtil.zRange("leaderboard", 0, 2, String.class); // 前3名
Set<String> topPlayersDesc = redisUtil.zReverseRange("leaderboard", 0, 2, String.class);

// 获取范围内元素（按分数）
Set<String> highScorers = redisUtil.zRangeByScore("leaderboard", 150.0, 300.0, String.class);

// 获取元素数量
Long count = redisUtil.zCard("leaderboard");
Long countInRange = redisUtil.zCount("leaderboard", 100.0, 200.0);

// 删除元素
redisUtil.zRemove("leaderboard", "player1");
redisUtil.zRemoveRangeByRank("leaderboard", 0, 0); // 删除排名最低的
redisUtil.zRemoveRangeByScore("leaderboard", 0.0, 100.0); // 删除分数范围内的
```

## 高级特性

### 1. 类型安全的泛型支持

RedisUtil的所有get方法都支持泛型，避免了类型转换的麻烦：

```java
// 不推荐：需要手动转换
Object obj = redisUtil.get("key");
User user = (User) obj;

// 推荐：类型安全
User user = redisUtil.get("key", User.class);
List<User> users = redisUtil.lRange("users", 0, -1, User.class);
Set<String> tags = redisUtil.setMembers("tags", String.class);
```

### 2. 自动序列化

RedisUtil使用Jackson进行对象序列化，支持复杂对象的自动序列化和反序列化：

```java
// 复杂对象
public class Order {
    private Long id;
    private List<OrderItem> items;
    private Map<String, Object> metadata;
    // getters and setters
}

// 直接存储和获取
Order order = new Order();
redisUtil.set("order:123", order);
Order savedOrder = redisUtil.get("order:123", Order.class);
```

### 3. 连接池配置

支持Lettuce连接池的详细配置：

```yaml
hdl:
  redis:
    single:
      url: localhost:6379
      maxActive: 20      # 最大连接数
      maxIdle: 10        # 最大空闲连接数
      minIdle: 2         # 最小空闲连接数
      maxWait: 5000      # 最大等待时间(毫秒)
```

## 注意事项

1. **自动配置排除**：本模块会自动排除Spring Boot的默认Redis配置，确保使用HDL的实现
2. **Bean命名规则**：
   - 单Redis：`redisTemplate`、`stringRedisTemplate`、`redisUtil`
   - 多Redis：`{datasourceName}RedisTemplate`、`{datasourceName}StringRedisTemplate`、`{datasourceName}RedisUtil`
3. **默认数据源**：多Redis配置时，可通过`defaultDatasource`指定默认数据源，未指定时使用第一个配置的数据源
4. **序列化方式**：Key使用String序列化，Value使用Jackson序列化，确保跨语言兼容性

## 故障排除

### 常见问题

1. **连接失败**：检查Redis服务是否启动，网络是否可达
2. **认证失败**：检查密码配置是否正确
3. **序列化错误**：确保存储的对象可以被Jackson序列化
4. **Bean注入失败**：检查@Qualifier注解是否正确使用

### 日志配置

```yaml
logging:
  level:
    com.haidilao.fd.redis.core: DEBUG
```

## 示例项目

完整的使用示例请参考：[示例项目链接]

## 配置参数详解

### RedisEnvironmentConfig 配置参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `url` | String | 无 | Redis连接地址，格式：`host:port`，集群/哨兵模式用`;`分隔多个节点 |
| `database` | Integer | 0 | Redis数据库索引（0-15） |
| `password` | String | 无 | Redis密码，可选 |
| `mode` | String | standalone | Redis部署模式：`standalone`、`cluster`、`sentinel` |
| `master` | String | 无 | 哨兵模式下的主节点名称 |
| `maxActive` | Integer | 8 | 连接池最大连接数 |
| `maxIdle` | Integer | 8 | 连接池最大空闲连接数 |
| `minIdle` | Integer | 0 | 连接池最小空闲连接数 |
| `maxWait` | Integer | -1 | 连接池最大等待时间（毫秒），-1表示无限等待 |

### 多数据源配置说明

```yaml
hdl:
  redis:
    defaultDatasource: primary    # 可选，指定默认数据源
    datasources:
      primary:                    # 数据源名称，用于Bean命名
        url: localhost:6379
        database: 0
        # 其他配置...
      secondary:
        url: localhost:6380
        database: 1
        # 其他配置...
```

生成的Bean名称规则：
- RedisTemplate: `{datasourceName}RedisTemplate`
- StringRedisTemplate: `{datasourceName}StringRedisTemplate`
- RedisUtil: `{datasourceName}RedisUtil`
- RedisConnectionFactory: `{datasourceName}RedisConnectionFactory`

## 最佳实践

### 1. 合理使用数据类型

```java
// ✅ 推荐：根据业务场景选择合适的数据类型
// 缓存用户信息 - 使用Hash
redisUtil.hPutAll("user:" + userId, userMap);

// 消息队列 - 使用List
redisUtil.lRightPush("message_queue", message);

// 标签系统 - 使用Set
redisUtil.sAdd("user_tags:" + userId, "java", "spring");

// 排行榜 - 使用Sorted Set
redisUtil.zAdd("score_board", userId, score);

// ❌ 不推荐：所有场景都使用String
redisUtil.set("user_tags:" + userId, JSON.toJSONString(tags));
```

### 2. 设置合理的过期时间

```java
// ✅ 推荐：根据业务需求设置过期时间
redisUtil.set("session:" + sessionId, user, 1800);        // 会话30分钟
redisUtil.set("cache:" + key, data, 3600);                // 缓存1小时
redisUtil.set("temp:" + key, data, 300);                  // 临时数据5分钟

// ❌ 不推荐：不设置过期时间，可能导致内存泄漏
redisUtil.set("cache:" + key, data);
```

### 3. 使用合适的Key命名规范

```java
// ✅ 推荐：使用有意义的Key命名
String userCacheKey = "user:cache:" + userId;
String sessionKey = "session:" + sessionId;
String lockKey = "lock:order:" + orderId;

// ❌ 不推荐：Key命名不规范
String key1 = "u" + userId;
String key2 = sessionId;
```

### 4. 批量操作优化性能

```java
// ✅ 推荐：使用批量操作
Map<String, Object> batch = new HashMap<>();
for (User user : users) {
    batch.put("user:" + user.getId(), user);
}
redisUtil.multiSet(batch);

// ❌ 不推荐：循环单个操作
for (User user : users) {
    redisUtil.set("user:" + user.getId(), user);
}
```

### 5. 异常处理

```java
// ✅ 推荐：适当的异常处理
try {
    User user = redisUtil.get("user:" + userId, User.class);
    if (user == null) {
        // 从数据库加载
        user = userService.loadFromDatabase(userId);
        redisUtil.set("user:" + userId, user, 3600);
    }
    return user;
} catch (Exception e) {
    log.error("Redis操作失败，降级到数据库查询", e);
    return userService.loadFromDatabase(userId);
}
```

## 性能优化建议

### 1. 连接池配置优化

```yaml
hdl:
  redis:
    single:
      url: localhost:6379
      maxActive: 50      # 根据并发量调整
      maxIdle: 20        # 保持一定数量的空闲连接
      minIdle: 5         # 最小空闲连接，避免冷启动
      maxWait: 3000      # 避免无限等待
```

### 2. 序列化优化

```java
// 对于简单数据类型，考虑使用StringRedisTemplate
@Autowired
private StringRedisTemplate stringRedisTemplate;

// 存储简单字符串
stringRedisTemplate.opsForValue().set("counter", "100");

// 对于复杂对象，使用RedisUtil的对象序列化
redisUtil.set("user:" + userId, user);
```

### 3. 内存使用优化

```java
// 使用Hash存储对象字段，节省内存
redisUtil.hPut("user:" + userId, "name", user.getName());
redisUtil.hPut("user:" + userId, "email", user.getEmail());

// 而不是存储整个对象
// redisUtil.set("user:" + userId, user);
```

## 监控和运维

### 1. 健康检查

```java
@Component
public class RedisHealthIndicator {

    @Autowired
    private RedisUtil redisUtil;

    public boolean isHealthy() {
        try {
            redisUtil.set("health_check", "ok", 10);
            String result = redisUtil.get("health_check", String.class);
            return "ok".equals(result);
        } catch (Exception e) {
            return false;
        }
    }
}
```

### 2. 性能监控

```java
@Aspect
@Component
public class RedisPerformanceMonitor {

    @Around("execution(* com.haidilao.fd.redis.core.util.RedisUtil.*(..))")
    public Object monitor(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;
            if (duration > 100) { // 超过100ms记录慢查询
                log.warn("Redis慢查询: {}, 耗时: {}ms",
                    joinPoint.getSignature().getName(), duration);
            }
            return result;
        } catch (Exception e) {
            log.error("Redis操作异常: {}", joinPoint.getSignature().getName(), e);
            throw e;
        }
    }
}
```

## 更新日志

- v1.0.0：初始版本，支持基础Redis操作
- v1.1.0：增加多Redis数据源支持
- v1.2.0：增加泛型支持，提升类型安全性
