package com.haidilao.fd.redis.core.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.haidilao.fd.redis.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.net.UnknownHostException;
import java.util.Map;

/**
 * Redis数据源处理器
 * 负责动态注册多个Redis数据源相关的Bean
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisDataSourceProcessor implements BeanDefinitionRegistryPostProcessor, ApplicationContextAware {

    private final RedisConfigProperties redisProperties;
    private ApplicationContext applicationContext;

    public RedisDataSourceProcessor(RedisConfigProperties redisProperties) {
        this.redisProperties = redisProperties;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        // 处理Redis配置
        Map<String, RedisConfigProperties.RedisEnvironmentConfig> datasources = redisProperties.getDatasources();
        RedisConfigProperties.RedisEnvironmentConfig singleConfig = redisProperties.getSingle();

        if (!CollectionUtils.isEmpty(datasources)) {
            // 多数据源配置
            log.info("检测到多Redis数据源配置，数据源数量: {}", datasources.size());
            registerMultipleDataSources(registry, datasources);
        } else if (singleConfig != null) {
            // 单数据源配置
            log.info("检测到单Redis数据源配置");
            registerSingleDataSource(registry, singleConfig);
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // 不需要处理
    }

    /**
     * 注册多个数据源
     */
    private void registerMultipleDataSources(BeanDefinitionRegistry registry, 
                                             Map<String, RedisConfigProperties.RedisEnvironmentConfig> datasources) {
        String defaultDatasource = redisProperties.getDefaultDatasource();
        boolean hasDefault = false;

        for (Map.Entry<String, RedisConfigProperties.RedisEnvironmentConfig> entry : datasources.entrySet()) {
            String datasourceName = entry.getKey();
            RedisConfigProperties.RedisEnvironmentConfig config = entry.getValue();

            // 注册连接工厂
            String connectionFactoryBeanName = datasourceName + "RedisConnectionFactory";
            registerConnectionFactory(registry, connectionFactoryBeanName, config);

            // 注册RedisTemplate
            String redisTemplateBeanName = datasourceName + "RedisTemplate";
            registerRedisTemplate(registry, redisTemplateBeanName, connectionFactoryBeanName);

            // 注册StringRedisTemplate
            String stringRedisTemplateBeanName = datasourceName + "StringRedisTemplate";
            registerStringRedisTemplate(registry, stringRedisTemplateBeanName, connectionFactoryBeanName);

            // 注册RedisUtil
            String redisUtilBeanName = datasourceName + "RedisUtil";
            registerRedisUtil(registry, redisUtilBeanName, redisTemplateBeanName);

            // 处理默认数据源
            boolean isDefault = false;
            if (StringUtils.hasText(defaultDatasource) && defaultDatasource.equals(datasourceName)) {
                isDefault = true;
                hasDefault = true;
            } else if (!StringUtils.hasText(defaultDatasource) && !hasDefault) {
                // 如果没有指定默认数据源，使用第一个作为默认
                isDefault = true;
                hasDefault = true;
            }

            if (isDefault) {
                // 注册默认Bean（不带前缀）
                registerDefaultBeans(registry, connectionFactoryBeanName, redisTemplateBeanName,stringRedisTemplateBeanName,
                        redisUtilBeanName);
            }

            log.info("注册Redis数据源: {}, 连接: {}, 数据库: {}, 默认: {}", 
                    datasourceName, config.getUrl(), config.getDatabase(), isDefault);
        }
    }

    /**
     * 注册单个数据源
     */
    private void registerSingleDataSource(BeanDefinitionRegistry registry, RedisConfigProperties.RedisEnvironmentConfig config) {
        // 注册连接工厂
        registerConnectionFactory(registry, "redisConnectionFactory", config);

        // 注册RedisTemplate
        registerRedisTemplate(registry, "redisTemplate", "redisConnectionFactory");

        // 注册StringRedisTemplate
        registerStringRedisTemplate(registry,"stringRedisTemplate","redisConnectionFactory");

        // 注册RedisUtil
        registerRedisUtil(registry, "redisUtil", "redisTemplate");

        log.info("注册单Redis数据源, 连接: {}, 数据库: {}", config.getUrl(), config.getDatabase());
    }

    /**
     * 注册默认Bean
     */
    private void registerDefaultBeans(BeanDefinitionRegistry registry, String connectionFactoryBeanName,
                                    String redisTemplateBeanName, String stringRedisTemplateBeanName,String redisUtilBeanName) {
        // 注册默认连接工厂（别名方式）
        if (!registry.containsBeanDefinition("redisConnectionFactory")) {
            registry.registerAlias(connectionFactoryBeanName, "redisConnectionFactory");
        }

        // 注册默认RedisTemplate（别名方式）
        if (!registry.containsBeanDefinition("redisTemplate")) {
            registry.registerAlias(redisTemplateBeanName, "redisTemplate");
        }

        // 注册默认StringRedisTemplate（别名方式）
        if (!registry.containsBeanDefinition("stringRedisTemplate")) {
            registry.registerAlias(stringRedisTemplateBeanName, "stringRedisTemplate");
        }

        // 注册默认RedisUtil（别名方式）
        if (!registry.containsBeanDefinition("redisUtil")) {
            registry.registerAlias(redisUtilBeanName, "redisUtil");
        }
    }

    /**
     * 注册连接工厂
     */
    private void registerConnectionFactory(BeanDefinitionRegistry registry, String beanName,
                                         RedisConfigProperties.RedisEnvironmentConfig config) {
        BeanDefinition beanDefinition = BeanDefinitionBuilder.genericBeanDefinition(RedisDataSourceProcessor.class)
                .setFactoryMethod("createConnectionFactory")
                .addConstructorArgValue(config)
                .getBeanDefinition();
        registry.registerBeanDefinition(beanName, beanDefinition);
    }

    /**
     * 注册RedisTemplate
     */
    private void registerRedisTemplate(BeanDefinitionRegistry registry, String beanName, String connectionFactoryBeanName) {
        BeanDefinition beanDefinition = BeanDefinitionBuilder.genericBeanDefinition(RedisCoreAutoConfiguration.class)
                .setFactoryMethod("redisTemplate")
                .addConstructorArgReference(connectionFactoryBeanName)
                .getBeanDefinition();
        registry.registerBeanDefinition(beanName, beanDefinition);
    }

    /**
     * 注册RedisUtil
     */
    private void registerRedisUtil(BeanDefinitionRegistry registry, String beanName, String redisTemplateBeanName) {
        BeanDefinition beanDefinition = BeanDefinitionBuilder.genericBeanDefinition(RedisUtil.class)
                .addConstructorArgReference(redisTemplateBeanName)
                .getBeanDefinition();
        registry.registerBeanDefinition(beanName, beanDefinition);
    }



    /**
     * 注册StringRedisTemplate
     */
    private void registerStringRedisTemplate(BeanDefinitionRegistry registry,String beanName, String connectionFactoryBeanName) {
        BeanDefinition beanDefinition = BeanDefinitionBuilder
                .genericBeanDefinition("org.springframework.data.redis.core.StringRedisTemplate")
                .addConstructorArgReference(connectionFactoryBeanName)
                .getBeanDefinition();
        registry.registerBeanDefinition(beanName, beanDefinition);
    }



    /**
     * 创建连接工厂的静态方法
     */
    public static RedisConnectionFactory createConnectionFactory(RedisConfigProperties.RedisEnvironmentConfig config) {
        HdlRedisConnectionFactory factory = new HdlRedisConnectionFactory(config);
        return factory.getLettuceConnectionFactory();
    }


    /**
     * Key 序列化器（String 类型）
     */
    public static RedisSerializer<String> keySerializer() {
        return new StringRedisSerializer();
    }

    /**
     * Value 序列化器（JSON 类型，带类信息）
     */
    public static RedisSerializer<Object> valueSerializer() {
        return new GenericJackson2JsonRedisSerializer(objectMapper());
    }


    public static ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 启用自动类型推断（存储@Class信息，反序列化时不丢失类型）
        objectMapper.activateDefaultTyping(
                LaissezFaireSubTypeValidator.instance,
                ObjectMapper.DefaultTyping.NON_FINAL,
                JsonTypeInfo.As.PROPERTY
        );
        return objectMapper;
    }

    /**
     * 创建RedisTemplate
     */
    public static RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // key采用String的序列化方式
        template.setKeySerializer(keySerializer());
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(keySerializer());
        // value序列化方式采用jackson
        template.setValueSerializer(valueSerializer());
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(valueSerializer());

        template.afterPropertiesSet();
        return template;
    }

    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory)
            throws UnknownHostException {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }
}
