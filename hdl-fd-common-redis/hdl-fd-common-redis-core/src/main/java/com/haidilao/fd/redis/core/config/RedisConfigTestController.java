package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Redis配置测试控制器
 * 仅在开发环境或测试环境启用
 * 
 * 启用方式：在配置文件中添加 hdl.redis.debug.enabled=true
 */
@Slf4j
@RestController
@RequestMapping("/hdl/redis/debug")
@ConditionalOnProperty(name = "hdl.redis.debug.enabled", havingValue = "true")
public class RedisConfigTestController {

    @Autowired
    private Environment environment;
    
    @Autowired(required = false)
    private RedisConfigProperties redisConfigProperties;

    /**
     * 获取Redis配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getRedisConfig() {
        Map<String, Object> result = new HashMap<>();
        
        // 基本信息
        result.put("timestamp", System.currentTimeMillis());
        result.put("activeProfiles", environment.getActiveProfiles());
        result.put("defaultProfiles", environment.getDefaultProfiles());
        
        // 配置属性对象
        if (redisConfigProperties != null) {
            Map<String, Object> configProps = new HashMap<>();
            configProps.put("datasources", redisConfigProperties.getDatasources());
            configProps.put("single", redisConfigProperties.getSingle());
            configProps.put("defaultDatasource", redisConfigProperties.getDefaultDatasource());
            result.put("redisConfigProperties", configProps);
        } else {
            result.put("redisConfigProperties", "RedisConfigProperties Bean未找到");
        }
        
        // 环境变量中的配置
        Map<String, String> envConfigs = new HashMap<>();
        String[] configKeys = {
            "hdl.redis.single.url",
            "hdl.redis.single.database", 
            "hdl.redis.single.password",
            "hdl.redis.single.mode",
            "hdl.redis.defaultDatasource"
        };
        
        for (String key : configKeys) {
            String value = environment.getProperty(key);
            if (value != null) {
                if (key.contains("password")) {
                    envConfigs.put(key, "***");
                } else {
                    envConfigs.put(key, value);
                }
            }
        }
        result.put("environmentConfigs", envConfigs);
        
        // 检查是否有Spring默认Redis配置
        Map<String, String> springConfigs = new HashMap<>();
        String[] springKeys = {
            "spring.redis.host",
            "spring.redis.port", 
            "spring.redis.url",
            "spring.redis.database",
            "spring.redis.password"
        };
        
        for (String key : springKeys) {
            String value = environment.getProperty(key);
            if (value != null) {
                if (key.contains("password")) {
                    springConfigs.put(key, "***");
                } else {
                    springConfigs.put(key, value);
                }
            }
        }
        result.put("springRedisConfigs", springConfigs);
        
        return result;
    }
    
    /**
     * 测试配置加载
     */
    @GetMapping("/test")
    public Map<String, Object> testConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 直接从Environment读取配置
            String url = environment.getProperty("hdl.redis.single.url");
            String database = environment.getProperty("hdl.redis.single.database");
            String password = environment.getProperty("hdl.redis.single.password");
            
            result.put("success", true);
            result.put("directRead", Map.of(
                "url", url != null ? url : "null",
                "database", database != null ? database : "null", 
                "password", password != null ? "***" : "null"
            ));
            
            // 通过ConfigurationProperties读取
            if (redisConfigProperties != null) {
                result.put("configPropertiesRead", Map.of(
                    "single", redisConfigProperties.getSingle() != null ? "已配置" : "null",
                    "datasources", redisConfigProperties.getDatasources() != null ? "已配置" : "null"
                ));
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("配置测试失败", e);
        }
        
        return result;
    }
}
