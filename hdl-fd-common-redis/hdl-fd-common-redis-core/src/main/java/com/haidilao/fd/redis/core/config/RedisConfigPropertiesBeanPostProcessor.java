package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * Redis配置属性Bean后处理器
 * 在RedisConfigProperties初始化完成后进行处理
 */
@Slf4j
@Component
public class RedisConfigPropertiesBeanPostProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof RedisConfigProperties) {
            RedisConfigProperties properties = (RedisConfigProperties) bean;
            log.info("RedisConfigProperties初始化完成，开始验证配置");
            log.info("配置详情: datasources={}, single={}, defaultDatasource={}", 
                    properties.getDatasources() != null ? properties.getDatasources().keySet() : "null", 
                    properties.getSingle() != null ? "已配置" : "null", 
                    properties.getDefaultDatasource());
            
            // 在这里可以进行配置验证或其他处理
            if (properties.getSingle() != null || properties.getDatasources() != null) {
                log.info("Redis配置验证通过！");
            } else {
                log.warn("Redis配置为空，请检查配置文件");
            }
        }
        return bean;
    }
}
