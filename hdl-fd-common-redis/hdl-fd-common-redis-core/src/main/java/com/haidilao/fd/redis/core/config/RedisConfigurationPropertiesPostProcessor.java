package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * Redis配置属性后处理器
 * 确保配置属性正确绑定
 */
@Slf4j
@Component
public class RedisConfigurationPropertiesPostProcessor implements BeanPostProcessor {

    private final Environment environment;

    public RedisConfigurationPropertiesPostProcessor(Environment environment) {
        this.environment = environment;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof RedisConfigProperties) {
            RedisConfigProperties properties = (RedisConfigProperties) bean;
            
            // 检查是否已经正确绑定
            if (properties.getSingle() == null && properties.getDatasources() == null) {
                log.warn("检测到Redis配置属性未正确绑定，尝试手动绑定...");
                
                try {
                    // 手动绑定配置
                    RedisConfigProperties boundProperties = Binder.get(environment)
                            .bind("hdl.redis", RedisConfigProperties.class)
                            .orElse(new RedisConfigProperties());
                    
                    // 复制绑定的属性到原Bean
                    properties.setSingle(boundProperties.getSingle());
                    properties.setDatasources(boundProperties.getDatasources());
                    properties.setDefaultDatasource(boundProperties.getDefaultDatasource());
                    
                    log.info("手动绑定Redis配置成功: single={}, datasources={}", 
                            properties.getSingle() != null ? "已配置" : "null",
                            properties.getDatasources() != null ? properties.getDatasources().keySet() : "null");
                    
                } catch (Exception e) {
                    log.error("手动绑定Redis配置失败", e);
                }
            } else {
                log.info("Redis配置属性已正确绑定");
            }
        }
        return bean;
    }
}
