package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Redis模块加载测试
 * 用于验证模块是否被正确加载
 */
@Slf4j
@Component
@Order(1) // 确保早期执行
public class RedisModuleLoadTest implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        log.info("=== HDL Redis Core 模块加载测试 ===");
        log.info("如果你看到这条日志，说明HDL Redis Core模块已被正确加载");
        log.info("模块路径: {}", this.getClass().getPackage().getName());
        log.info("=== 模块加载测试完成 ===");
    }
}
