package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis核心自动配置类
 * 支持单Redis和多Redis配置
 *
 * 注意：此配置会完全替代Spring Boot的RedisAutoConfiguration
 * 确保默认的redisTemplate使用我们的实现
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass(RedisTemplate.class)
public class RedisCoreAutoConfiguration {

    public RedisCoreAutoConfiguration() {
        log.info("HDL Redis Core 自动配置类已加载！");
        log.info("当前类路径: {}", this.getClass().getName());
    }


    /**
     * Redis配置诊断工具
     */
    @Bean
    public RedisConfigDiagnostic redisConfigDiagnostic() {
        log.info("注册HDL Redis配置诊断工具");
        return new RedisConfigDiagnostic();
    }

    /**
     * Redis配置属性测试
     */
    @Bean
    public RedisConfigPropertiesTest redisConfigPropertiesTest() {
        log.info("注册Redis配置属性测试");
        return new RedisConfigPropertiesTest();
    }

    /**
     * Redis数据源配置处理器
     * 负责根据配置创建相应的Redis Bean
     * 使用@DependsOn确保RedisConfigProperties先初始化
     */
    @Bean
    @DependsOn("redisConfigProperties")
    public RedisDataSourceProcessor redisDataSourceProcessor(RedisConfigProperties redisProperties) {
        log.info("初始化HDL Redis配置处理器");
        log.info("配置属性详情: datasources={}, single={}, defaultDatasource={}",
                redisProperties.getDatasources() != null ? redisProperties.getDatasources().keySet() : "null",
                redisProperties.getSingle() != null ? "已配置" : "null",
                redisProperties.getDefaultDatasource());

        // 验证配置
        if (redisProperties.getDatasources() == null && redisProperties.getSingle() == null) {
            log.error("错误：未找到任何Redis配置！");
            log.error("请检查配置文件中是否包含 hdl.redis.single 或 hdl.redis.datasources 配置");
            throw new IllegalStateException("Redis配置不能为空，请检查配置文件");
        }

        log.info("Redis配置验证成功，开始创建数据源处理器");
        return new RedisDataSourceProcessor(redisProperties);
    }


}
