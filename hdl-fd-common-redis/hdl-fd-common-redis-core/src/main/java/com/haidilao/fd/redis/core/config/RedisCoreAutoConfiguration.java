package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis核心自动配置类
 * 支持单Redis和多Redis配置
 *
 * 注意：此配置会完全替代Spring Boot的RedisAutoConfiguration
 * 确保默认的redisTemplate使用我们的实现
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass(RedisTemplate.class)
@EnableConfigurationProperties(RedisConfigProperties.class)
public class RedisCoreAutoConfiguration {


    /**
     * Redis数据源配置处理器
     * 负责根据配置创建相应的Redis Bean
     */
    @Bean
    public RedisDataSourceProcessor redisDataSourceProcessor(RedisConfigProperties redisProperties) {
        log.info("初始化HDL Redis配置处理器");
        log.info("配置属性详情: datasources={}, single={}, defaultDatasource={}",
                redisProperties.getDatasources(),
                redisProperties.getSingle(),
                redisProperties.getDefaultDatasource());

        // 调试信息：检查配置是否为空
        if (redisProperties.getDatasources() == null && redisProperties.getSingle() == null) {
            log.warn("警告：Redis配置为空！请检查配置文件中的 hdl.redis 配置");
        }

        return new RedisDataSourceProcessor(redisProperties);
    }


}
