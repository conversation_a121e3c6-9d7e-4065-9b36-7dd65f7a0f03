package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;

/**
 * Redis配置诊断工具
 * 用于排查配置属性注入问题
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisConfigDiagnostic implements ApplicationListener<ApplicationReadyEvent> {

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        Environment env = event.getApplicationContext().getEnvironment();

        log.info("=== HDL Redis 配置诊断开始 ===");

        // 检查Profile
        String[] activeProfiles = env.getActiveProfiles();
        String[] defaultProfiles = env.getDefaultProfiles();
        log.info("当前激活的Profile: {}", activeProfiles.length > 0 ? String.join(",", activeProfiles) : "无");
        log.info("默认Profile: {}", defaultProfiles.length > 0 ? String.join(",", defaultProfiles) : "无");

        // 检查配置源
        checkConfigSources(env);

        // 检查配置前缀
        String[] prefixes = {"hdl.redis", "hdl.redis.single", "hdl.redis.datasources"};
        for (String prefix : prefixes) {
            checkConfigPrefix(env, prefix);
        }

        // 检查具体配置项
        checkSpecificConfigs(env);

        // 检查是否有Spring默认Redis配置
        checkSpringRedisConfig(env);

        log.info("=== HDL Redis 配置诊断完成 ===");
    }
    
    private void checkConfigPrefix(Environment env, String prefix) {
        log.info("检查配置前缀: {}", prefix);
        
        // 尝试获取一些常见的配置项
        String[] commonKeys = {"url", "database", "password", "mode", "maxActive"};
        boolean hasAnyConfig = false;
        
        for (String key : commonKeys) {
            String fullKey = prefix + "." + key;
            String value = env.getProperty(fullKey);
            if (value != null) {
                log.info("  找到配置: {} = {}", fullKey, maskPassword(fullKey, value));
                hasAnyConfig = true;
            }
        }
        
        if (!hasAnyConfig) {
            log.warn("  未找到 {} 相关配置", prefix);
        }
    }
    
    private void checkSpecificConfigs(Environment env) {
        log.info("检查具体配置项:");
        
        // 单Redis配置
        String singleUrl = env.getProperty("hdl.redis.single.url");
        if (singleUrl != null) {
            log.info("  单Redis配置: hdl.redis.single.url = {}", singleUrl);
            log.info("  单Redis数据库: hdl.redis.single.database = {}", 
                    env.getProperty("hdl.redis.single.database", "0"));
        }
        
        // 多Redis配置
        String defaultDs = env.getProperty("hdl.redis.defaultDatasource");
        if (defaultDs != null) {
            log.info("  默认数据源: hdl.redis.defaultDatasource = {}", defaultDs);
        }
        
        // 尝试检查多数据源配置
        checkMultiDataSourceConfig(env);
    }
    
    private void checkMultiDataSourceConfig(Environment env) {
        // 常见的数据源名称
        String[] commonDataSources = {"cache", "session", "primary", "secondary", "default"};
        
        for (String dsName : commonDataSources) {
            String urlKey = "hdl.redis.datasources." + dsName + ".url";
            String url = env.getProperty(urlKey);
            if (url != null) {
                log.info("  多数据源配置: {} = {}", urlKey, url);
                log.info("    数据库: hdl.redis.datasources.{}.database = {}", 
                        dsName, env.getProperty("hdl.redis.datasources." + dsName + ".database", "0"));
            }
        }
    }
    
    private void checkConfigSources(Environment env) {
        log.info("检查配置源:");
        if (env instanceof org.springframework.core.env.AbstractEnvironment) {
            org.springframework.core.env.AbstractEnvironment abstractEnv = (org.springframework.core.env.AbstractEnvironment) env;
            abstractEnv.getPropertySources().forEach(propertySource -> {
                log.info("  配置源: {} (类型: {})", propertySource.getName(), propertySource.getClass().getSimpleName());
            });
        }
    }

    private void checkSpringRedisConfig(Environment env) {
        log.info("检查Spring默认Redis配置:");
        String springRedisHost = env.getProperty("spring.redis.host");
        String springRedisUrl = env.getProperty("spring.redis.url");
        if (springRedisHost != null || springRedisUrl != null) {
            log.warn("  发现Spring默认Redis配置:");
            log.warn("    spring.redis.host = {}", springRedisHost);
            log.warn("    spring.redis.url = {}", springRedisUrl);
            log.warn("  注意：HDL Redis使用 hdl.redis 前缀，不是 spring.redis");
        } else {
            log.info("  未发现Spring默认Redis配置");
        }
    }

    private String maskPassword(String key, String value) {
        if (key.toLowerCase().contains("password") && value != null && !value.isEmpty()) {
            return "***";
        }
        return value;
    }
}
