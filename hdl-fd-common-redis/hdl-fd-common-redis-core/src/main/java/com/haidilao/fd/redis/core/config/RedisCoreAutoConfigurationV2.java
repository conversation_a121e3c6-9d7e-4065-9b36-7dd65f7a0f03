package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis核心自动配置类 - 使用@Lazy解决方案
 */
@Slf4j
@Configuration
@ConditionalOnClass(RedisTemplate.class)
@EnableConfigurationProperties(RedisConfigProperties.class)
public class RedisCoreAutoConfigurationV2 {

    /**
     * Redis数据源配置处理器
     * 使用@Lazy延迟初始化，确保配置属性已经绑定
     */
    @Bean
    @Lazy
    public RedisDataSourceProcessor redisDataSourceProcessorLazy(RedisConfigProperties redisProperties) {
        log.info("延迟初始化HDL Redis配置处理器");
        log.info("配置属性详情: datasources={}, single={}, defaultDatasource={}", 
                redisProperties.getDatasources() != null ? redisProperties.getDatasources().keySet() : "null", 
                redisProperties.getSingle() != null ? "已配置" : "null", 
                redisProperties.getDefaultDatasource());
        
        return new RedisDataSourceProcessor(redisProperties);
    }
}
