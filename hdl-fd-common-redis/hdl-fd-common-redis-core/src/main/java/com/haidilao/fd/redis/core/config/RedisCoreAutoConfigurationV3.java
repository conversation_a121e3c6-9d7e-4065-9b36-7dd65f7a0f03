package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis核心自动配置类 - 使用ApplicationListener解决方案
 */
@Slf4j
@Configuration
@ConditionalOnClass(RedisTemplate.class)
@EnableConfigurationProperties(RedisConfigProperties.class)
public class RedisCoreAutoConfigurationV3 implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private RedisConfigProperties redisProperties;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("应用启动完成，开始初始化Redis配置");
        log.info("配置属性详情: datasources={}, single={}, defaultDatasource={}", 
                redisProperties.getDatasources() != null ? redisProperties.getDatasources().keySet() : "null", 
                redisProperties.getSingle() != null ? "已配置" : "null", 
                redisProperties.getDefaultDatasource());
        
        // 在这里可以动态注册Redis相关Bean
        RedisDataSourceProcessor processor = new RedisDataSourceProcessor(redisProperties);
        // 手动处理Bean注册逻辑
    }
}
