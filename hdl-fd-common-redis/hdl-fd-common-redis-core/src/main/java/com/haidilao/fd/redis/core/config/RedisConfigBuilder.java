package com.haidilao.fd.redis.core.config;

import com.haidilao.fd.redis.core.enums.RedisModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Redis配置构建器
 * 用于从Environment直接构建Redis配置对象
 * 解决ConfigurationProperties绑定时机问题
 */
@Slf4j
public class RedisConfigBuilder {

    /**
     * 从Environment构建RedisConfigProperties
     */
    public static RedisConfigProperties buildFromEnvironment(Environment environment) {
        log.info("开始从Environment构建Redis配置");
        
        RedisConfigProperties properties = new RedisConfigProperties();
        
        // 构建单Redis配置
        RedisConfigProperties.RedisEnvironmentConfig singleConfig = buildSingleConfig(environment);
        if (singleConfig != null) {
            properties.setSingle(singleConfig);
            log.info("构建单Redis配置成功: url={}, database={}", singleConfig.getUrl(), singleConfig.getDatabase());
        }
        
        // 构建多Redis配置
        Map<String, RedisConfigProperties.RedisEnvironmentConfig> datasources = buildMultiConfig(environment);
        if (!datasources.isEmpty()) {
            properties.setDatasources(datasources);
            log.info("构建多Redis配置成功: {}", datasources.keySet());
        }
        
        // 设置默认数据源
        String defaultDatasource = environment.getProperty("hdl.redis.defaultDatasource");
        if (StringUtils.hasText(defaultDatasource)) {
            properties.setDefaultDatasource(defaultDatasource);
            log.info("设置默认数据源: {}", defaultDatasource);
        }
        
        return properties;
    }
    
    /**
     * 构建单Redis配置
     */
    private static RedisConfigProperties.RedisEnvironmentConfig buildSingleConfig(Environment environment) {
        String url = environment.getProperty("hdl.redis.single.url");
        if (!StringUtils.hasText(url)) {
            log.debug("未找到单Redis配置");
            return null;
        }
        
        RedisConfigProperties.RedisEnvironmentConfig config = new RedisConfigProperties.RedisEnvironmentConfig();
        config.setUrl(url);
        config.setDatabase(environment.getProperty("hdl.redis.single.database", Integer.class, 0));
        config.setPassword(environment.getProperty("hdl.redis.single.password"));
        config.setMode(environment.getProperty("hdl.redis.single.mode", RedisModeEnum.STANDALONE.getName()));
        config.setMaster(environment.getProperty("hdl.redis.single.master"));
        config.setMaxActive(environment.getProperty("hdl.redis.single.maxActive", Integer.class, 8));
        config.setMaxIdle(environment.getProperty("hdl.redis.single.maxIdle", Integer.class, 8));
        config.setMinIdle(environment.getProperty("hdl.redis.single.minIdle", Integer.class, 0));
        config.setMaxWait(environment.getProperty("hdl.redis.single.maxWait", Integer.class, -1));
        
        return config;
    }
    
    /**
     * 构建多Redis配置
     */
    private static Map<String, RedisConfigProperties.RedisEnvironmentConfig> buildMultiConfig(Environment environment) {
        Map<String, RedisConfigProperties.RedisEnvironmentConfig> datasources = new HashMap<>();
        
        // 常见的数据源名称
        String[] commonNames = {"cache", "session", "primary", "secondary", "default", "main"};
        
        for (String name : commonNames) {
            String urlKey = "hdl.redis.datasources." + name + ".url";
            String url = environment.getProperty(urlKey);
            
            if (StringUtils.hasText(url)) {
                RedisConfigProperties.RedisEnvironmentConfig config = new RedisConfigProperties.RedisEnvironmentConfig();
                String prefix = "hdl.redis.datasources." + name;
                
                config.setUrl(url);
                config.setDatabase(environment.getProperty(prefix + ".database", Integer.class, 0));
                config.setPassword(environment.getProperty(prefix + ".password"));
                config.setMode(environment.getProperty(prefix + ".mode", RedisModeEnum.STANDALONE.getName()));
                config.setMaster(environment.getProperty(prefix + ".master"));
                config.setMaxActive(environment.getProperty(prefix + ".maxActive", Integer.class, 8));
                config.setMaxIdle(environment.getProperty(prefix + ".maxIdle", Integer.class, 8));
                config.setMinIdle(environment.getProperty(prefix + ".minIdle", Integer.class, 0));
                config.setMaxWait(environment.getProperty(prefix + ".maxWait", Integer.class, -1));
                
                datasources.put(name, config);
                log.info("找到数据源配置: {} -> {}", name, url);
            }
        }
        
        return datasources;
    }
}
