package com.haidilao.fd.redis.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.PostConstruct;

/**
 * Redis核心自动配置类 - 使用@PostConstruct解决方案
 */
@Slf4j
@Configuration
@ConditionalOnClass(RedisTemplate.class)
@EnableConfigurationProperties(RedisConfigProperties.class)
@Order(1000) // 确保在其他配置之后执行
public class RedisCoreAutoConfigurationV4 {

    @Autowired
    private RedisConfigProperties redisProperties;

    @PostConstruct
    public void initRedisConfig() {
        log.info("@PostConstruct 初始化Redis配置");
        log.info("配置属性详情: datasources={}, single={}, defaultDatasource={}", 
                redisProperties.getDatasources() != null ? redisProperties.getDatasources().keySet() : "null", 
                redisProperties.getSingle() != null ? "已配置" : "null", 
                redisProperties.getDefaultDatasource());
        
        // 在这里配置属性已经正确绑定
        if (redisProperties.getSingle() != null || redisProperties.getDatasources() != null) {
            log.info("Redis配置绑定成功！");
        }
    }
}
