package com.haidilao.fd.redis.core.config;

import com.haidilao.fd.redis.core.enums.RedisModeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.Map;
import java.util.Optional;

/**
 * Redis核心配置属性
 * 支持单Redis配置和多Redis配置
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@ConfigurationProperties(prefix = "hdl.redis")
public class RedisConfigProperties {

    /**
     * 多Redis数据源配置
     * 配置示例：
     * hdl:
     *   redis:
     *     datasources:
     *       cache:
     *         url: localhost:6379
     *         database: 0
     *       session:
     *         url: localhost:6379
     *         database: 1
     */
    private Map<String, RedisEnvironmentConfig> datasources;

    /**
     * 单Redis配置（向后兼容）
     * 当只配置单个Redis时，可以直接在redis下配置
     */
    private RedisEnvironmentConfig single;

    /**
     * 默认Redis数据源名称
     * 当配置多个数据源时，指定哪个作为默认数据源
     * 如果不指定，将使用第一个配置的数据源作为默认
     */
    private String defaultDatasource;

    /**
     * 配置属性初始化后的调试信息
     */
    @PostConstruct
    public void init() {
        log.info("HDL Redis配置属性初始化完成:");
        log.info("  - datasources: {}", datasources != null ? datasources.keySet() : "null");
        log.info("  - single: {}", single != null ? "已配置" : "null");
        log.info("  - defaultDatasource: {}", defaultDatasource);

        if (datasources == null && single == null) {
            log.error("错误：未找到任何Redis配置！请检查配置文件中的 hdl.redis 配置");
        }
    }

    @Data
    public static class RedisEnvironmentConfig {
        private Integer database = 0;

        private String master;


        /**
         * 集群 哨兵 模式
         */
        private String mode = RedisModeEnum.STANDALONE.getName();

        /**
         * 集群 哨兵 ; 分隔
         */
        private String url;

        /**
         * 密码
         */
        private String password;

        /**
         * 池中 “空闲” 连接的最大数量。使用负值表示无限数量的空闲连接。
         */
        private Integer maxIdle = 8;

        /**
         * 目标是池中要维护的最小空闲连接数。此设置仅在正向情况下有效。
         */
        private Integer minIdle = 0;

        /**
         * 池在给定时间内可分配的最大连接数。使用负值表示无限制。
         */
        private Integer maxActive = 8;

        /**
         * 连接分配应在池耗尽时抛出异常之前阻塞最长时间。使用负值无限期阻塞。
         */
        private Integer maxWait = -1;


        /**
         * Gets max wait.
         *
         * @return the max wait
         */
        public Duration getMaxWait() {
            return Optional.ofNullable(maxWait)
                    .map(it -> Duration.ofMillis(maxWait)).orElse(Duration.ofMillis(-1));
        }
    }
}
