package com.haidilao.fd.redis.core.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Redis配置属性测试类
 * 用于验证配置绑定是否正常工作
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "hdl.redis")
public class RedisConfigPropertiesTest {

    private String testValue;
    private TestSingle single;
    
    @PostConstruct
    public void init() {
        log.info("=== Redis配置属性测试 ===");
        log.info("testValue: {}", testValue);
        log.info("single: {}", single);
        if (single != null) {
            log.info("single.url: {}", single.getUrl());
            log.info("single.database: {}", single.getDatabase());
        }
        log.info("=== 配置属性测试完成 ===");
    }
    
    @Data
    public static class TestSingle {
        private String url;
        private Integer database;
        private String password;
    }
}
