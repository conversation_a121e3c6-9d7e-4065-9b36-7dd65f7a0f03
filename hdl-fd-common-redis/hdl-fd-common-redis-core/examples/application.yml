# HDL Redis Core 配置示例

# 示例1: 单Redis配置（适用于简单应用）
hdl:
  redis:
    single:
      url: localhost:6379
      database: 0
      password: your_redis_password
      mode: standalone
      maxActive: 20
      maxIdle: 10
      minIdle: 2
      maxWait: 3000

---
# 示例2: 多Redis配置（适用于复杂应用）
spring:
  profiles: multi-redis

hdl:
  redis:
    defaultDatasource: cache  # 指定默认数据源
    datasources:
      # 缓存Redis - 用于应用缓存
      cache:
        url: redis-cache.example.com:6379
        database: 0
        password: cache_password
        mode: standalone
        maxActive: 50
        maxIdle: 20
        minIdle: 5
        maxWait: 3000
      
      # 会话Redis - 用于用户会话
      session:
        url: redis-session.example.com:6379
        database: 1
        password: session_password
        mode: standalone
        maxActive: 30
        maxIdle: 15
        minIdle: 3
        maxWait: 2000
      
      # 消息队列Redis - 用于异步消息
      queue:
        url: redis-queue.example.com:6379
        database: 2
        password: queue_password
        mode: standalone
        maxActive: 40
        maxIdle: 20
        minIdle: 5
        maxWait: 5000

---
# 示例3: Redis集群配置
spring:
  profiles: cluster

hdl:
  redis:
    single:
      url: redis-node1:6379;redis-node2:6379;redis-node3:6379;redis-node4:6379;redis-node5:6379;redis-node6:6379
      password: cluster_password
      mode: cluster
      maxActive: 100
      maxIdle: 50
      minIdle: 10
      maxWait: 3000

---
# 示例4: Redis哨兵配置
spring:
  profiles: sentinel

hdl:
  redis:
    single:
      url: sentinel1:26379;sentinel2:26379;sentinel3:26379
      master: mymaster
      database: 0
      password: sentinel_password
      mode: sentinel
      maxActive: 60
      maxIdle: 30
      minIdle: 5
      maxWait: 3000

---
# 示例5: 开发环境配置
spring:
  profiles: dev

hdl:
  redis:
    single:
      url: localhost:6379
      database: 0
      mode: standalone
      maxActive: 8
      maxIdle: 8
      minIdle: 0
      maxWait: -1

# 开发环境日志配置
logging:
  level:
    com.haidilao.fd.redis.core: DEBUG
    org.springframework.data.redis: DEBUG

---
# 示例6: 生产环境配置
spring:
  profiles: prod

hdl:
  redis:
    defaultDatasource: primary
    datasources:
      primary:
        url: ${REDIS_PRIMARY_URL:redis-primary.prod.com:6379}
        database: ${REDIS_PRIMARY_DB:0}
        password: ${REDIS_PRIMARY_PASSWORD}
        mode: ${REDIS_PRIMARY_MODE:standalone}
        maxActive: ${REDIS_PRIMARY_MAX_ACTIVE:100}
        maxIdle: ${REDIS_PRIMARY_MAX_IDLE:50}
        minIdle: ${REDIS_PRIMARY_MIN_IDLE:10}
        maxWait: ${REDIS_PRIMARY_MAX_WAIT:3000}
      
      cache:
        url: ${REDIS_CACHE_URL:redis-cache.prod.com:6379}
        database: ${REDIS_CACHE_DB:1}
        password: ${REDIS_CACHE_PASSWORD}
        mode: ${REDIS_CACHE_MODE:standalone}
        maxActive: ${REDIS_CACHE_MAX_ACTIVE:80}
        maxIdle: ${REDIS_CACHE_MAX_IDLE:40}
        minIdle: ${REDIS_CACHE_MIN_IDLE:8}
        maxWait: ${REDIS_CACHE_MAX_WAIT:2000}

# 生产环境日志配置
logging:
  level:
    com.haidilao.fd.redis.core: INFO
    root: WARN

---
# 示例7: 测试环境配置
spring:
  profiles: test

hdl:
  redis:
    single:
      url: localhost:6379
      database: 15  # 使用最后一个数据库避免冲突
      mode: standalone
      maxActive: 10
      maxIdle: 5
      minIdle: 1
      maxWait: 1000

# 测试环境可以启用更详细的日志
logging:
  level:
    com.haidilao.fd.redis.core: TRACE

---
# 示例8: Docker环境配置
spring:
  profiles: docker

hdl:
  redis:
    single:
      url: redis:6379  # Docker服务名
      database: 0
      mode: standalone
      maxActive: 20
      maxIdle: 10
      minIdle: 2
      maxWait: 3000

---
# 示例9: Kubernetes环境配置
spring:
  profiles: k8s

hdl:
  redis:
    single:
      url: redis-service.default.svc.cluster.local:6379
      database: 0
      password: ${REDIS_PASSWORD}
      mode: standalone
      maxActive: ${REDIS_MAX_ACTIVE:50}
      maxIdle: ${REDIS_MAX_IDLE:25}
      minIdle: ${REDIS_MIN_IDLE:5}
      maxWait: ${REDIS_MAX_WAIT:3000}

---
# 示例10: 高可用配置（主从+哨兵）
spring:
  profiles: ha

hdl:
  redis:
    datasources:
      # 主业务Redis（哨兵模式）
      primary:
        url: sentinel1.ha.com:26379;sentinel2.ha.com:26379;sentinel3.ha.com:26379
        master: redis-primary
        database: 0
        password: ${REDIS_PRIMARY_PASSWORD}
        mode: sentinel
        maxActive: 100
        maxIdle: 50
        minIdle: 10
        maxWait: 3000
      
      # 缓存Redis（集群模式）
      cache:
        url: cache-node1:6379;cache-node2:6379;cache-node3:6379;cache-node4:6379
        password: ${REDIS_CACHE_PASSWORD}
        mode: cluster
        maxActive: 80
        maxIdle: 40
        minIdle: 8
        maxWait: 2000
