package com.haidilao.fd.example.service;

import com.haidilao.fd.redis.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务示例 - 展示HDL Redis Core的各种使用方式
 */
@Slf4j
@Service
public class UserService {

    // 单Redis配置时的注入方式
    @Autowired
    private RedisUtil redisUtil;

    // 多Redis配置时的注入方式
    @Autowired
    @Qualifier("cacheRedisUtil")
    private RedisUtil cacheRedisUtil;

    @Autowired
    @Qualifier("sessionRedisUtil")
    private RedisUtil sessionRedisUtil;

    /**
     * 示例1: 基础的用户缓存操作
     */
    public User getUserById(Long userId) {
        String cacheKey = "user:" + userId;
        
        try {
            // 先从缓存获取
            User user = redisUtil.get(cacheKey, User.class);
            if (user != null) {
                log.info("从缓存获取用户: {}", userId);
                return user;
            }
            
            // 缓存未命中，从数据库加载
            user = loadUserFromDatabase(userId);
            if (user != null) {
                // 缓存用户信息，1小时过期
                redisUtil.set(cacheKey, user, 3600);
                log.info("用户信息已缓存: {}", userId);
            }
            
            return user;
        } catch (Exception e) {
            log.error("Redis操作失败，降级到数据库查询", e);
            return loadUserFromDatabase(userId);
        }
    }

    /**
     * 示例2: 使用Hash存储用户详细信息
     */
    public void saveUserProfile(Long userId, UserProfile profile) {
        String hashKey = "user:profile:" + userId;
        
        try {
            Map<String, Object> profileMap = new HashMap<>();
            profileMap.put("nickname", profile.getNickname());
            profileMap.put("avatar", profile.getAvatar());
            profileMap.put("bio", profile.getBio());
            profileMap.put("lastLoginTime", profile.getLastLoginTime());
            
            // 使用Hash存储，节省内存
            redisUtil.hPutAll(hashKey, profileMap);
            redisUtil.expire(hashKey, 7200); // 2小时过期
            
            log.info("用户资料已保存: {}", userId);
        } catch (Exception e) {
            log.error("保存用户资料失败", e);
        }
    }

    /**
     * 示例3: 获取用户资料
     */
    public UserProfile getUserProfile(Long userId) {
        String hashKey = "user:profile:" + userId;
        
        try {
            Map<Object, Object> profileMap = redisUtil.hGetAll(hashKey);
            if (profileMap.isEmpty()) {
                return null;
            }
            
            UserProfile profile = new UserProfile();
            profile.setNickname((String) profileMap.get("nickname"));
            profile.setAvatar((String) profileMap.get("avatar"));
            profile.setBio((String) profileMap.get("bio"));
            profile.setLastLoginTime((Date) profileMap.get("lastLoginTime"));
            
            return profile;
        } catch (Exception e) {
            log.error("获取用户资料失败", e);
            return null;
        }
    }

    /**
     * 示例4: 用户标签管理（使用Set）
     */
    public void addUserTags(Long userId, String... tags) {
        String setKey = "user:tags:" + userId;
        
        try {
            redisUtil.sAdd(setKey, (Object[]) tags);
            redisUtil.expire(setKey, 86400); // 24小时过期
            
            log.info("用户标签已添加: userId={}, tags={}", userId, Arrays.toString(tags));
        } catch (Exception e) {
            log.error("添加用户标签失败", e);
        }
    }

    /**
     * 示例5: 获取用户所有标签
     */
    public Set<String> getUserTags(Long userId) {
        String setKey = "user:tags:" + userId;
        
        try {
            return redisUtil.setMembers(setKey, String.class);
        } catch (Exception e) {
            log.error("获取用户标签失败", e);
            return new HashSet<>();
        }
    }

    /**
     * 示例6: 用户积分排行榜（使用Sorted Set）
     */
    public void updateUserScore(Long userId, double score) {
        String zsetKey = "user:leaderboard";
        
        try {
            redisUtil.zAdd(zsetKey, userId.toString(), score);
            log.info("用户积分已更新: userId={}, score={}", userId, score);
        } catch (Exception e) {
            log.error("更新用户积分失败", e);
        }
    }

    /**
     * 示例7: 获取积分排行榜前N名
     */
    public List<UserRank> getTopUsers(int limit) {
        String zsetKey = "user:leaderboard";
        
        try {
            // 获取分数最高的前N名用户
            Set<String> userIds = redisUtil.zReverseRange(zsetKey, 0, limit - 1, String.class);
            
            List<UserRank> rankings = new ArrayList<>();
            int rank = 1;
            for (String userId : userIds) {
                Double score = redisUtil.zScore(zsetKey, userId);
                UserRank userRank = new UserRank();
                userRank.setUserId(Long.valueOf(userId));
                userRank.setScore(score);
                userRank.setRank(rank++);
                rankings.add(userRank);
            }
            
            return rankings;
        } catch (Exception e) {
            log.error("获取排行榜失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 示例8: 用户会话管理（使用多Redis数据源）
     */
    public void saveUserSession(String sessionId, User user) {
        try {
            // 使用专门的会话Redis存储
            sessionRedisUtil.set("session:" + sessionId, user, 1800); // 30分钟
            log.info("用户会话已保存: sessionId={}, userId={}", sessionId, user.getId());
        } catch (Exception e) {
            log.error("保存用户会话失败", e);
        }
    }

    /**
     * 示例9: 获取用户会话
     */
    public User getUserSession(String sessionId) {
        try {
            return sessionRedisUtil.get("session:" + sessionId, User.class);
        } catch (Exception e) {
            log.error("获取用户会话失败", e);
            return null;
        }
    }

    /**
     * 示例10: 用户操作计数器
     */
    public long incrementUserAction(Long userId, String action) {
        String counterKey = "user:counter:" + userId + ":" + action;
        
        try {
            Long count = redisUtil.incr(counterKey);
            // 设置当天过期
            redisUtil.expireAt(counterKey, getTomorrowStartTime());
            
            log.info("用户操作计数: userId={}, action={}, count={}", userId, action, count);
            return count;
        } catch (Exception e) {
            log.error("用户操作计数失败", e);
            return 0;
        }
    }

    /**
     * 示例11: 批量获取用户信息
     */
    public List<User> getUsersByIds(List<Long> userIds) {
        try {
            // 构建缓存键列表
            List<String> cacheKeys = userIds.stream()
                    .map(id -> "user:" + id)
                    .toList();
            
            // 批量获取
            List<User> users = redisUtil.multiGet(cacheKeys, User.class);
            
            // 处理缓存未命中的情况
            List<User> result = new ArrayList<>();
            for (int i = 0; i < userIds.size(); i++) {
                User user = users.get(i);
                if (user == null) {
                    // 从数据库加载并缓存
                    user = loadUserFromDatabase(userIds.get(i));
                    if (user != null) {
                        redisUtil.set(cacheKeys.get(i), user, 3600);
                    }
                }
                if (user != null) {
                    result.add(user);
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量获取用户失败", e);
            return userIds.stream()
                    .map(this::loadUserFromDatabase)
                    .filter(Objects::nonNull)
                    .toList();
        }
    }

    /**
     * 示例12: 用户在线状态管理
     */
    public void setUserOnline(Long userId) {
        String onlineKey = "user:online:" + userId;
        
        try {
            redisUtil.set(onlineKey, "1", 300); // 5分钟过期，需要定期刷新
            log.info("用户上线: {}", userId);
        } catch (Exception e) {
            log.error("设置用户在线状态失败", e);
        }
    }

    /**
     * 示例13: 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        String onlineKey = "user:online:" + userId;
        
        try {
            return redisUtil.hasKey(onlineKey);
        } catch (Exception e) {
            log.error("检查用户在线状态失败", e);
            return false;
        }
    }

    // 辅助方法
    private User loadUserFromDatabase(Long userId) {
        // 模拟从数据库加载用户
        log.info("从数据库加载用户: {}", userId);
        // 实际实现中这里会调用数据库查询
        return new User(userId, "用户" + userId, "user" + userId + "@example.com");
    }

    private Date getTomorrowStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}
